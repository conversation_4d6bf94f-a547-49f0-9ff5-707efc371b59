export const SPONSORED_LISTING = {
  PERFORMANCE_HEADER: 'Your listings performance',
  CREATE_SPONSORED_LISTING: 'Create a new sponsored listing campaign',
  CREATE_SPONSORED_LISTING_DESCRIPTION:
    "With Sponsored Listings, your business profile appears higher in search and Request-a-Quote (RaQ) results when customers are looking for a relevant job in your area. This means more eyes on your services, leading to more job opportunities. On average trades see a +35% increase in profile impressions and +30% increase in leads after using Sponsored Listing. It's a simple, effective way to get in front of potential customers when they need you most.",
  CREATE_SPONSORED_LISTING_HEADER:
    'Ready to get noticed? Create a listing and start attracting more local work',
  SPONSORED_LISTING_EMPTY_STATE: 'No sponsored listings to show',
  TOAST_ERROR: 'Unable to retrieve campaigns statistics right now',
  WHAT_ARE_SPONSORED_LISTINGS_TITLE: 'What are sponsored listings?',
  HOW_SPONSORED_LISTINGS_WORK_TITLE: 'How sponsored listings work',
  HOW_SPONSORED_LISTINGS_WORK_CONTENT:
    "Here's how it works: Sponsored Listings puts your trade business front and center when homeowners are actively searching for services like yours. Appear at the top of search results, boost your visibility, and drive more leads with our powerful real-time auction system that places your business in prime search spots across our platform.\n\nYou only pay for the exposure—no hidden fees or complicated contracts. It's a flexible, hassle-free way to take control of your marketing and ensure you're always ahead of the competition.\n\nThe benefits are clear: greater visibility, increased leads, and more business growth—all with minimal effort. Don't miss out on this opportunity to supercharge your presence on Checkatrade.",
  EMPTY_STATE:
    "Sponsored Listing campaign is not available to you at the moment. To get started, please sign up to a PAYG or a Fixed Plan leads campaign — once it's active, you'll be able to enable a Sponsored Listings campaign.",
};

export const METRIC_CARDS = {
  IMPRESSIONS: {
    LABEL: 'Impressions',
    QUERY_LABEL:
      'The total number of times your sponsored listing is displayed to users.',
  },
  CLICKS: {
    LABEL: 'Clicks',
    QUERY_LABEL:
      'The total number of times your sponsored listing is clicked. Please note that clicks are deduplicated, meaning if a user clicks multiple times within a certain timeframe, it is only counted as one click.',
  },
  CPC: {
    LABEL: 'Cost per click (CPC)',
    QUERY_LABEL:
      'The amount you pay each time someone clicks on your sponsored listing.',
  },
  CTR: {
    LABEL: 'Avg. Click through rate',
    QUERY_LABEL:
      'The percentage of impressions that result in clicks. This measures how many users who viewed your sponsored listing clicked on it.',
  },
  TOTAL_SPEND: {
    LABEL: 'Total spend',
    QUERY_LABEL:
      'The total amount spent on sponsored listings over a specific period, calculated by multiplying the number of clicks by the cost per click.',
  },
};

export const CREATE_SPONSORED_LISTING = {
  CATEGORY_SELECTION_STEP: {
    TITLE_PART_1: 'What type of ',
    TITLE_PART_2: 'category',
    TITLE_PART_3: ' would you like to boost?',
    SUBTITLE:
      'Select the service you would like to be included in sponsored listings',
    LIVE_INDICATOR: 'Live',
    ERROR_TITLE: 'Error loading campaigns',
    ERROR_DESCRIPTION:
      'There was a problem loading your campaigns. Please try again later.',
  },
  SUBCATEGORIES_AND_GEOS_STEP: {
    TITLE: 'Campaign targeting',
    DESCRIPTION:
      'We\'ve pre-selected all relevant types of work and work areas for the sponsored campaign for you. You can change them to suit you business needs by clicking "edit" buttons below',
    NO_SUBCATEGORIES_SELECTED: 'No types of work selected',
    NO_GEOS_SELECTED: 'No work areas selected',
    CONTINUE_BUTTON: 'Continue',
  },
  BUDGET_AND_BID_STRATEGY_STEP: {
    TITLE: 'Campaign budget and bid strategy',
    DESCRIPTION:
      "Set the monthly budget for your sponsored search and sponsored Request-a-Quote (RAQ) campaigns. We've pre-selected your bidding strategy for maximum exposure. Click the edit button to change your bidding strategy.",
    CONTINUE_BUTTON: 'Continue',
  },
  SUMMARY_STEP: {
    TITLE: 'Sponsored campaign set-up confirmation',
    DESCRIPTION:
      'Please confirm the details below before creating your sponsored campaign. If you have any questions, please contact our support team at 0333 014 6190.',
    SUBMIT_BUTTON: 'Create sponsored listing',
  },
  COMMON: {
    EDIT_BUTTON: 'Edit',
  },
  SUBCATEGORIES_ACCORDION: {
    TITLE: 'Types of Work',
    MODAL_TITLE: 'Types of Work',
    MODAL_SUBTITLE: 'What type of work would you like?',
    MODAL_DESCRIPTION:
      "We've pre-selected the most common types of work, but you can change them as needed.",
    SAVE: 'Save',
    SELECT_ALL: 'Select all',
    DESELECT_ALL: 'Deselect all',
    RESET_TO_MOST_POPULAR: 'Reset to most popular',
    SELECTED: 'selected',
  },
  GEOGRAPHIES_ACCORDION: {
    TITLE: 'Work Areas',
    MODAL_TITLE: 'Work Areas',
    MODAL_SUBTITLE: 'Select the work areas you want to target',
    MODAL_DESCRIPTION:
      "We've pre-selected all your work areas, but you can change them to suit your business needs.",
    SAVE: 'Save',
    SELECT_ALL: 'Select all',
    DESELECT_ALL: 'Deselect all',
    RESET_TO_MOST_POPULAR: 'Reset to most popular',
    SELECTED: 'selected',
  },
  LOCAL_EDIT_MODE: {
    MANUAL_BID_LISTING: 'manual-bid-listings',
    MANUAL_BID_RAQ: 'manual-bid-raq',
    DELAY_START: 'delay-start',
    END_DATE: 'end-date',
    ADVANCED_LISTING_SETTINGS: 'Advanced listing settings',
    SET_MANUAL_BID_FOR_LISTING: 'Set manual bid amount for Sponsored Search',
    SET_CUSTOM_BID_AMOUNT_LISTING:
      'Sponsored search campaign will help you to appear at the top of the search results. We will manage your bids automatically, but in case you want to set them up manually, please set the manual bid per click below.',
    SET_MANUAL_BID_FOR_RAQ: 'Set manual bid amount for Sponsored RaQ',
    SET_CUSTOM_BID_AMOUNT_RAQ:
      'Sponsored RaQ campaign will help you to appear in the Request-a-Quote results. We will manage your bids automatically, but in case you want to set them up manually, please set the manual bid below.',
    DELAY_START_DATE: 'Delay start date',
    CHOOSE_WHEN_RUNNING:
      'Your sponsored campaign will start immediately once set up. In case you want to delay the start date, please set the start date below.',
    SET_END_DATE: 'Set end date',
    CHOOSE_WHEN_STOP_RUNNING:
      'Your sponsored campaign will be always-on until you choose to deactivate it. In case you want to set an end date, please set it below.',
    MODAL_TITLE_DELAY_START: 'Set Delay Start Date',
    MODAL_TITLE_END_DATE: 'Set End Date',
    MODAL_TITLE_MANUAL_BID_LISTING: 'Set Manual Bid for Sponsored Search',
    MODAL_TITLE_MANUAL_BID_RAQ: 'Set Manual Bid for Sponsored RaQ',
    REMOVE: 'Remove',
    SET: 'Set',
    LISTINGS_BID_RANGE: 'Sponsored Search bid: £',
    RAQ_BID_RANGE: 'Sponsored RaQ bid: £',
    RANGE_SEPARATOR: ' - £',
    BID_AMOUNT_LABEL: 'Bid amount (£)',
    BID_PLACEHOLDER: 'Enter value (e.g. 1.50)',
    START_DATE_LABEL: 'Start date',
    END_DATE_LABEL: 'End date',
    DATE_PLACEHOLDER: 'MMM D, YYYY',
    SAVE: 'Save',
    SELECT_DATE: 'Select date',
    LISTING_PACING: 'Listing pacing',
    LISTING_PACING_DESCRIPTION:
      'Pacing determines how your budget is spent when advertising opportunities arise.',
    MODAL_TITLE_LISTING_PACING: 'Set Listing Pacing',
    LISTING_PACING_MODAL_DESCRIPTION:
      'Choose how you want your budget to be spent throughout the campaign period.',
    SET_PACING: 'Set pacing',
    EVENLY_PACING: 'Evenly',
    ASAP_PACING: 'ASAP',
    ACCELERATED_PACING: 'Accelerated',
  },
  MANUAL_BIDS: {
    ONSITE_SPONSORED_LISTINGS: 'Onsite Sponsored Listings',
    RAQ: 'RaQ',
  },
  BUDGET_CARD: {
    TITLE: 'Monthly budget',
    EDIT: 'Edit',
    CURRENCY_SYMBOL: '£',
    DEFAULT_BUDGET: '200.00',
    BUDGET_SPLIT_TITLE: 'Budget Split:',
    SPONSORED_RAQ: 'Sponsored RaQ: £',
    SPONSORED_SEARCH: 'Sponsored search: £',
    RAQ_PERCENTAGE: ' (70%)',
    SEARCH_PERCENTAGE: ' (30%)',
    MODAL_TITLE: 'Set Monthly Budget',
    MODAL_DESCRIPTION: 'Please reach out to support to set a higher budget',
    BUDGET_INFO_TEXT:
      "We'll pace your spend to ensure you don't go over budget. Some months you may not use the full amount, depending on demand. To get more leads, try adding more job types or areas.",
    SAVE_BUTTON: 'Confirm',
  },
};

export const minimumBudget = 50;
export const maximumBudget = 1000;

export const BID_STRATEGY_CARD = {
  TITLE: 'Bidding strategy',
  EDIT: 'Edit',
  MODAL_TITLE: 'Select Bidding Strategy',
  MODAL_OVERVIEW_TITLE: 'Bid strategy overview:',
  STRATEGY_DESCRIPTIONS: {
    LEADER:
      'High bids for maximum exposure prioritising top placements, with a faster budget spend.',
    COMPETITOR:
      'A mix of competitiveness and budget control aiming for strong visibility without rapid spend.',
    PLAYER:
      'Lower bids for moderate exposure maximising efficiency while maintaining control over spend.',
  },
};

export const BUDGET_PACING = {
  ASAP: 'Invests your budget into every opportunity immediately. May burn budget quickly but can maximize fast impact.',
  EVENLY:
    'Spreads your budget evenly across days and hours. Great for consistent, predictable delivery.',
  ACCELERATED:
    'A mix of Even + ASAP: spends evenly but prioritizes peak demand. Ideal for capitalizing on busy periods while maintaining longer activity.',
};

export const SPONSORED_LISTINGS_CARD = {
  SPONSORED_LISTINGS: 'Sponsored Listings',
  SPONSORED_SEARCH: 'Sponsored Search',
  SPONSORED_RAQ: 'Sponsored RAQ',
  MONTHLY_BUDGET_USED: 'Monthly budget used',
  LISTINGS: 'Listings',
  RAQ: 'RequestAQuote',
};
