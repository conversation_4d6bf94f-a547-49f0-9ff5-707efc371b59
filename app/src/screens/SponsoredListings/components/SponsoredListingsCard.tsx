import React, { useMemo } from 'react';
import { View, TouchableOpacity } from 'react-native';
import {
  Icon,
  ProgressBar,
  Typography,
} from '@cat-home-experts/react-native-components';
import {
  createMortarStyles,
  createTestIds,
} from '@cat-home-experts/react-native-utilities';
import { formatPrice } from 'src/screens/Campaigns/utilities/formatPrice';
import { palette as staticPalette } from '@cat-home-experts/react-native-utilities/dist/styles/styles';
import { SponsoredListingsStatusTag } from './SponsoredListingsStatusTag';
import { SponsoredListingsCardProps } from '../types';
import { SPONSORED_LISTINGS_CARD } from '../constants';

const TEST_IDS = createTestIds('sponsored-campaign-card', {
  CONTAINER: 'container',
  STATUS_TAG: 'status-tag',
  CAMPAIGN_TYPE: 'campaign-type',
  BID_STRATEGY: 'bid-strategy',
  BUDGET_SECTION: 'budget-section',
  PROGRESS_BAR: 'progress-bar',
  MENU_BUTTON: 'menu-button',
});

export const SponsoredListingsCard: React.FC<SponsoredListingsCardProps> = ({
  campaign,
  onMenuPress,
}) => {
  const isActive = useMemo(() => {
    return Boolean(campaign.isActive && !campaign.isPaused);
  }, [campaign.isActive, campaign.isPaused]);

  const campaignTypeText = useMemo(() => {
    if (!campaign.mdpSponsoredSearch?.searchTypes[0]) {
      return null;
    }

    const searchType = campaign.mdpSponsoredSearch?.searchTypes[0];

    if (searchType === SPONSORED_LISTINGS_CARD.LISTINGS) {
      return SPONSORED_LISTINGS_CARD.SPONSORED_SEARCH;
    }

    if (searchType === SPONSORED_LISTINGS_CARD.RAQ) {
      return SPONSORED_LISTINGS_CARD.SPONSORED_RAQ;
    }

    return null;
  }, [campaign.mdpSponsoredSearch?.searchTypes]);

  const bidStrategyText = useMemo(() => {
    if (campaign.mdpSponsoredSearch?.bidStrategy) {
      const biddingStrategy = campaign.mdpSponsoredSearch?.bidStrategy;
      return biddingStrategy;
    }

    return null;
  }, [campaign.mdpSponsoredSearch?.bidStrategy]);

  const budgetUsage = useMemo(() => {
    const currentBudget = campaign.currentBudgetAndBalance;
    if (!currentBudget) {
      return {
        spent: 0,
        total: 0,
        percentage: 0,
        spentText: '£0.00',
        totalText: '£0.00',
      };
    }

    const spent = currentBudget.invoiceBalance || 0;
    const total = currentBudget.maxBudget || 0;
    const percentage = total > 0 ? Math.min(spent / total, 1) : 0;

    return {
      spent,
      total,
      percentage,
      spentText: formatPrice(spent),
      totalText: formatPrice(total),
    };
  }, [campaign.currentBudgetAndBalance]);

  return (
    <View style={styles.container} testID={TEST_IDS.CONTAINER}>
      <View style={styles.header}>
        <Typography
          useVariant="bodyMedium"
          style={styles.campaignType}
          testID={TEST_IDS.CAMPAIGN_TYPE}
        >
          {campaignTypeText}
        </Typography>
        <TouchableOpacity
          onPress={onMenuPress}
          style={styles.menuButton}
          testID={TEST_IDS.MENU_BUTTON}
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <Icon name="ellipsis" size={20} />
        </TouchableOpacity>
      </View>
      <View style={styles.statusBidRow}>
        <SponsoredListingsStatusTag
          isActive={isActive}
          testID={TEST_IDS.STATUS_TAG}
        />
        <View style={styles.bidStrategyTag}>
          <Typography
            useVariant="labelSemiBold"
            style={styles.bidStrategyText}
            testID={TEST_IDS.BID_STRATEGY}
          >
            {bidStrategyText}
          </Typography>
        </View>
      </View>
      <View style={styles.budgetSection} testID={TEST_IDS.BUDGET_SECTION}>
        <View style={styles.budgetRow}>
          <Typography useVariant="labelRegular" style={styles.budgetLabel}>
            {SPONSORED_LISTINGS_CARD.MONTHLY_BUDGET_USED}
          </Typography>
          <Typography useVariant="bodySemiBold" style={styles.budgetText}>
            {`${budgetUsage.spentText} / ${budgetUsage.totalText}`}
          </Typography>
        </View>
        <ProgressBar
          progress={budgetUsage.percentage}
          containerStyle={styles.progressBarContainer}
          color={staticPalette.mortarV3.tokenDefault700}
        />
      </View>
    </View>
  );
};

SponsoredListingsCard.testIds = TEST_IDS;

const styles = createMortarStyles(({ palette, spacing }) => ({
  container: {
    backgroundColor: palette.mortarV3.tokenNeutral0,
    borderRadius: spacing(1),
    padding: spacing(2),
    borderWidth: 1,
    borderColor: palette.mortarV3.tokenNeutral200,
    shadowColor: palette.mortarV3.tokenNeutral900,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing(1.5),
  },
  menuButton: {
    padding: spacing(0.5),
  },
  campaignType: {
    color: palette.mortarV3.tokenNeutral900,
  },
  statusBidRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing(1),
    marginBottom: spacing(2),
  },
  bidStrategyTag: {
    backgroundColor: palette.mortarV3.tokenDefault200,
    paddingHorizontal: spacing(1.5),
    paddingVertical: spacing(0.5),
    borderRadius: spacing(2),
  },
  bidStrategyText: {
    color: palette.mortarV3.tokenDefault800,
  },
  budgetSection: {
    gap: spacing(1),
  },
  budgetRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  budgetLabel: {
    color: palette.mortarV3.tokenNeutral600,
  },
  budgetText: {
    color: palette.mortarV3.tokenNeutral900,
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: palette.mortarV3.tokenNeutral200,
    borderRadius: 3,
  },
}));
