import { z } from 'zod';
import { safeNativeEnum } from 'src/data/schemas/helpers/safeNativeEnum';
import { coerceNullishToOptional } from 'src/data/schemas/helpers/coerceNullishToOptional';
import { timestampToDate } from 'src/data/schemas/helpers/timestampToDate';
import {
  BiddingStrategyApiEnum,
  CampaignTypeEnum,
  GeographyApiSchema,
  RateBidsArrayApiSchema,
} from 'src/data/schemas/api/campaigns/schemas';

export enum PaymentCadenceEnum {
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUALLY = 'ANNUALLY',
}

export enum MdpSearchTypeDto {
  Listings = 'Listings',
  RequestAQuote = 'RequestAQuote',
}

export enum BidStrategyTypeDto {
  None = 'None',
  Player = 'Player',
  Leader = 'Leader',
  Competitor = 'Competitor',
}

export enum PacingTypeDto {
  Asap = 'Asap',
  Evenly = 'Evenly',
  Accelerated = 'Accelerated',
}

const CategorySchema = z.object({
  categoryId: z.number().int().positive().describe('The ID of the category'),
  name: z.string(),
  parentCategoryId: z
    .number()
    .int()
    .positive()
    .optional()
    .describe("The sub-category's parent category"),
  isSearchable: z
    .boolean()
    .describe(
      'If the company should be found in searches for the primary category',
    ),
});

const SubCategorySchema = z.object({
  categoryId: z.number().int().positive().describe('The ID of the category'),
  name: z.string().nullable(),
  parentCategoryId: z
    .number()
    .int()
    .positive()
    .optional()
    .describe("The sub-category's parent category"),
});

const CurrentBudgetSchema = z.object({
  budgetBoostAmount: z.number(),
  isMinimumCommitmentActive: z.boolean(),
  maxBudget: z
    .number()
    .nonnegative()
    .describe('The max budget the trade has agreed to for this Period'),
  maxSpend: z.number().nonnegative(),
  minimumCommitmentAmount: z.number(),
  minimumCommitmentUntil: z.coerce
    .date()
    .describe('The minimum commitment until date (first day of a month)')
    .nullable(),
  period: z.coerce.date().describe('The budget period (first day of a month)'),
});

const BudgetAndBalancesSchema = z.object({
  balance: z
    .number()
    .nonnegative()
    .describe(
      'The balance is the total cost of the leads delivered this Period, including any free leads (budget boost). It can be greater than the MaxBudget.',
    ),
  invoiceBalance: z
    .number()
    .nonnegative()
    .describe(
      'The invoice balance is the total cost of the leads delivered this Period, excluding any free leads (budget boost). It can be greater than the MaxBudget. This is the amount the trade will actually be charged.',
    ),
  threshold: z.number(),
  daysRemainingInPeriod: z.number(),
  leadCount: z.number().int().nonnegative(),
  clickCount: z.number().int().nonnegative(),
  cumulativeLeadCount: z.number().int().nonnegative(),
  leadCommitmentThreshold: z.number().nullable().optional(),
  ...CurrentBudgetSchema.shape,
});

export type BudgetAndBalancesApiType = z.infer<typeof BudgetAndBalancesSchema>;

const DiscountDto = z.object({
  fixed: z.number().optional(),
  percentage: z.number().optional(),
  numberOfMonths: z.number().optional(),
  lastDiscountedBudgetPeriod: z.coerce.date().optional(),
});

const LeadCommitmentDto = z.object({
  min: z.number().optional(),
  max: z.number().optional(),
  monthlyDistribution: z.object({}).optional(),
});

const FixedCampaignDetailsDto = z.object({
  price: z.number().optional(),
  monthsCommitted: z.number().optional(),
  commitmentStartDate: z.coerce.date().optional(),
  commitmentRenewalDate: z.coerce.date().optional(),
  paymentCadence: safeNativeEnum(PaymentCadenceEnum).default(
    PaymentCadenceEnum.MONTHLY,
  ),
  discount: coerceNullishToOptional(DiscountDto),
  leadCommitment: coerceNullishToOptional(LeadCommitmentDto),
  expectedLeadCount: coerceNullishToOptional(LeadCommitmentDto),
});

const SponsorshipCampaignDetailsDto = z.object({
  isSearchSponsored: z.boolean(),
  isRequestAQuoteSponsored: z.boolean(),
  searchRateMultiplier: z.number().optional(),
  requestAQuoteRateMultiplier: z.number().optional(),
});

const PplCampaignDetailsDto = z.object({
  biddingStrategyType: safeNativeEnum(BiddingStrategyApiEnum),
  isDirectoryOptOut: z.boolean().optional(),
  discount: coerceNullishToOptional(DiscountDto).optional(),
  leadCommitment: coerceNullishToOptional(LeadCommitmentDto).optional(),
  rateBids: RateBidsArrayApiSchema,
});

const MdpSponsoredSearchDto = z.object({
  primaryCampaignId: z.string(),
  endDate: z.coerce.date().optional(),
  bidAmount: z.number().nullable(),
  searchTypes: z.array(safeNativeEnum(MdpSearchTypeDto)),
  pacing: safeNativeEnum(PacingTypeDto).optional(),
  bidStrategy: safeNativeEnum(BidStrategyTypeDto).optional(),
});

export const CampaignDtoSchema = z
  .object({
    campaignId: z.string(),
    companyId: z.string().optional(),
    campaignType: safeNativeEnum(CampaignTypeEnum).default(
      CampaignTypeEnum.Ppl,
    ),
    isActive: z.boolean().optional(),
    isPaused: z.boolean().optional(),
    pausedUntil: coerceNullishToOptional(z.string().date()).optional(),
    category: CategorySchema,
    subCategories: z.array(SubCategorySchema).optional(),
    geographies: z.array(GeographyApiSchema).optional(),
    budgetsAndBalances: z.record(BudgetAndBalancesSchema).optional(),
    currentBudgetPeriod: z.coerce.date().optional(),
    budgetCycleDay: z.number().int().optional(),
    currentBudget: CurrentBudgetSchema,
    currentBudgetAndBalance: BudgetAndBalancesSchema,
    futureMonthMaxBudget: z.number().optional(),
    fixed: FixedCampaignDetailsDto.optional(),
    sponsorship: SponsorshipCampaignDetailsDto.optional(),
    ppl: PplCampaignDetailsDto.optional(),
    dateCreated: coerceNullishToOptional(
      z.string().datetime({ offset: true }),
    ).optional(),
    dateUpdated: coerceNullishToOptional(
      z.string().datetime({ offset: true }),
    ).optional(),
    dateBalanceLastUpdated: coerceNullishToOptional(timestampToDate).optional(),
    createdBy: coerceNullishToOptional(z.string()).optional(),
    updatedBy: coerceNullishToOptional(z.string()).optional(),
    mdpSponsoredSearch: MdpSponsoredSearchDto.optional(),
  })
  .describe(
    'The definition of each campaign to be displayed in the Trade app. NOTE this is a subset of the field available in Firestore.',
  );

export const CampaignDtoPageResult = z.object({
  items: z.array(CampaignDtoSchema),
  skip: z.number(),
  top: z.number(),
  count: z.number(),
});

export type CampaignDtoType = z.infer<typeof CampaignDtoSchema>;

export type CampaignDtoPageResultType = z.infer<typeof CampaignDtoPageResult>;
